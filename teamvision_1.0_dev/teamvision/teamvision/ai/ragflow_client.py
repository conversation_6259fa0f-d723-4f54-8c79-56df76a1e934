# coding=utf-8
"""
RAGFlow Agent客户端
用于调用RAGFlow的AI服务生成测试用例
"""

import requests
import json
from typing import Dict, List, Optional, Any
from django.conf import settings
from dataclasses import dataclass

from gatesidelib.common.simplelogger import SimpleLogger



@dataclass
class TestCaseGenerationRequest:
    """测试用例生成请求"""
    requirement_description: str  # 需求描述
    generation_type: str  # 生成类型：'functional', 'boundary', 'exception', 'integration'
    case_count: int = 10  # 生成用例数量# 优先级：'high', 'medium', 'low'


@dataclass
class TestCaseItem:
    """单个测试用例"""
    title: str
    description: str
    precondition: str
    test_steps: List[str]
    expected_result: str
    priority: int
    test_type: str
    tags: List[str]


@dataclass
class TestCaseGenerationResponse:
    """测试用例生成响应"""
    success: bool
    message: str
    test_cases: List[TestCaseItem]
    generation_id: str
    metadata: Dict[str, Any]


class RAGFlowClient:
    """RAGFlow客户端"""
    
    def __init__(self):
        self.base_url = getattr(settings, 'RAGFLOW_BASE_URL', 'http://localhost:9380')
        self.api_key = getattr(settings, 'RAGFLOW_API_KEY', '')
        self.agent_id = getattr(settings, 'RAGFLOW_AGENT_ID', '')
        self.timeout = getattr(settings, 'RAGFLOW_TIMEOUT', 30)
        
        self.headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {self.api_key}',
        }
    
    def _make_request(self, method: str, endpoint: str, data: Dict = None) -> Dict:
        """发送HTTP请求"""
        url = f"{self.base_url}{endpoint}"
        SimpleLogger.info(f"RAGFlow请求: {url}, 参数: {data}")

        try:
            if method.upper() == 'POST':
                response = requests.post(
                    url, 
                    headers=self.headers, 
                    json=data, 
                    timeout=self.timeout
                )
            elif method.upper() == 'GET':
                response = requests.get(
                    url, 
                    headers=self.headers, 
                    params=data, 
                    timeout=self.timeout
                )
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")

            SimpleLogger.info(f"RAGFlow响应: {response.text}")
            response.raise_for_status()
            return response.json()
            
        except requests.exceptions.RequestException as e:
            SimpleLogger.exception(f"RAGFlow API请求失败: {str(e)}")
            raise Exception(f"RAGFlow API调用失败: {str(e)}")
        except json.JSONDecodeError as e:
            SimpleLogger.exception(f"RAGFlow API响应解析失败: {str(e)}")
            raise Exception(f"RAGFlow API响应格式错误: {str(e)}")
    
    def generate_test_cases(self, request: TestCaseGenerationRequest) -> TestCaseGenerationResponse:
        """生成测试用例"""
        
        # 构建提示词
        prompt = self._build_test_case_prompt(request)
        
        # 调用RAGFlow Agent
        api_data = {
            'agent_id': self.agent_id,
            'message': prompt,
            'stream': False,
            'session_id': f"testcase_gen_{hash(request.requirement_description)}",
            'context': {
                'generation_type': request.generation_type,
                'case_count': request.case_count,
            }
        }
        
        try:
            response_data = self._make_request('POST', '/api/v1/agent/chat', api_data)
            # 解析RAGFlow响应
            return self._parse_ragflow_response(response_data, request)
            
        except Exception as e:
            SimpleLogger.exception(f"生成测试用例失败: {str(e)}")
            return TestCaseGenerationResponse(
                success=False,
                message=f"生成失败: {str(e)}",
                test_cases=[],
                generation_id="",
                metadata={}
            )
    
    def _build_test_case_prompt(self, request: TestCaseGenerationRequest) -> str:
        """构建测试用例生成提示词"""
        
        prompt_template = """
你是一个专业的软件测试工程师，请根据以下需求信息生成高质量的测试用例。

## 需求信息
**需求描述**: {requirement_description}
**测试类型**: {generation_type}
**生成数量**: {case_count}个

## 生成要求
1. 测试用例应该覆盖功能的主要场景和边界条件
2. 每个测试用例包含：标题、描述、前置条件、测试步骤、预期结果
3. 测试步骤要具体、可执行
4. 预期结果要明确、可验证
5. 根据重要性设置优先级（1-高，2-中，3-低）

## 输出格式
请以JSON格式输出，结构如下：
```json
{{
  "test_cases": [
    {{
      "title": "测试用例标题",
      "description": "测试用例描述",
      "precondition": "前置条件",
      "test_steps": ["步骤1", "步骤2", "步骤3"],
      "expected_result": "预期结果",
      "priority": 1,
      "test_type": "functional",
      "tags": ["标签1", "标签2"]
    }}
  ]
}}
```

请开始生成测试用例：
        """.format(
            requirement_description=request.requirement_description,
            generation_type=request.generation_type,
            case_count=request.case_count,
        )
        
        return prompt_template.strip()
    
    def _parse_ragflow_response(self, response_data: Dict, request: TestCaseGenerationRequest) -> TestCaseGenerationResponse:
        """解析RAGFlow响应"""
        
        try:
            # 获取AI生成的内容
            ai_message = response_data.get('data', {}).get('answer', '')
            
            if not ai_message:
                raise ValueError("RAGFlow返回空响应")
            
            # 尝试从响应中提取JSON
            test_cases_data = self._extract_json_from_response(ai_message)
            
            if not test_cases_data or 'test_cases' not in test_cases_data:
                raise ValueError("无法解析测试用例数据")
            
            # 转换为TestCaseItem对象
            test_cases = []
            for case_data in test_cases_data['test_cases']:
                test_case = TestCaseItem(
                    title=case_data.get('title', ''),
                    description=case_data.get('description', ''),
                    precondition=case_data.get('precondition', ''),
                    test_steps=case_data.get('test_steps', []),
                    expected_result=case_data.get('expected_result', ''),
                    priority=case_data.get('priority', 2),
                    test_type=case_data.get('test_type', request.generation_type),
                    tags=case_data.get('tags', [])
                )
                test_cases.append(test_case)
            
            return TestCaseGenerationResponse(
                success=True,
                message="测试用例生成成功",
                test_cases=test_cases,
                generation_id=response_data.get('data', {}).get('id', ''),
                metadata={
                    'generation_time': response_data.get('data', {}).get('create_time', ''),
                    'model_used': response_data.get('data', {}).get('model', ''),
                    'request_params': {
                        'generation_type': request.generation_type,
                        'case_count': request.case_count
                    }
                }
            )
            
        except Exception as e:
            SimpleLogger.exception(f"解析RAGFlow响应失败: {str(e)}")
            return TestCaseGenerationResponse(
                success=False,
                message=f"响应解析失败: {str(e)}",
                test_cases=[],
                generation_id="",
                metadata={}
            )
    
    def _extract_json_from_response(self, response_text: str) -> Dict:
        """从响应文本中提取JSON数据"""
        
        # 尝试直接解析JSON
        try:
            return json.loads(response_text)
        except json.JSONDecodeError:
            pass
        
        # 尝试从markdown代码块中提取JSON
        import re
        json_pattern = r'```json\s*(.*?)\s*```'
        matches = re.findall(json_pattern, response_text, re.DOTALL)
        
        for match in matches:
            try:
                return json.loads(match)
            except json.JSONDecodeError:
                continue
        
        # 尝试从普通代码块中提取JSON
        code_pattern = r'```\s*(.*?)\s*```'
        matches = re.findall(code_pattern, response_text, re.DOTALL)
        
        for match in matches:
            try:
                return json.loads(match)
            except json.JSONDecodeError:
                continue
        
        # 如果都失败了，尝试查找JSON对象
        json_obj_pattern = r'\{.*\}'
        matches = re.findall(json_obj_pattern, response_text, re.DOTALL)
        
        for match in matches:
            try:
                return json.loads(match)
            except json.JSONDecodeError:
                continue
        
        raise ValueError("无法从响应中提取有效的JSON数据")
    
    def optimize_test_case(self, test_case_data: Dict, optimization_type: str = 'quality') -> Dict:
        """优化测试用例"""
        
        prompt = f"""
请优化以下测试用例，优化类型：{optimization_type}

原测试用例：
{json.dumps(test_case_data, ensure_ascii=False, indent=2)}

请从以下方面进行优化：
1. 测试步骤的完整性和可执行性
2. 预期结果的明确性
3. 边界条件的覆盖
4. 异常场景的考虑

请返回优化后的测试用例，格式与原格式相同。
        """
        
        api_data = {
            'agent_id': self.agent_id,
            'message': prompt,
            'stream': False,
            'session_id': f"optimize_{hash(str(test_case_data))}"
        }
        
        try:
            response_data = self._make_request('POST', '/api/v1/agent/chat', api_data)
            ai_message = response_data.get('data', {}).get('answer', '')
            
            optimized_data = self._extract_json_from_response(ai_message)
            return optimized_data
            
        except Exception as e:
            SimpleLogger.exception(f"测试用例优化失败: {str(e)}")
            return test_case_data  # 返回原数据
    
    def analyze_test_coverage(self, requirement_text: str, test_cases: List[Dict]) -> Dict:
        """分析测试覆盖度"""
        
        test_cases_text = json.dumps(test_cases, ensure_ascii=False, indent=2)
        
        prompt = f"""
请分析以下测试用例对需求的覆盖情况：

需求描述：
{requirement_text}

测试用例：
{test_cases_text}

请从以下维度分析：
1. 功能覆盖度（百分比）
2. 边界条件覆盖
3. 异常场景覆盖
4. 缺失的测试场景
5. 改进建议

请以JSON格式返回分析结果。
        """
        
        api_data = {
            'agent_id': self.agent_id,
            'message': prompt,
            'stream': False,
            'session_id': f"coverage_analysis_{hash(requirement_text)}"
        }
        
        try:
            response_data = self._make_request('POST', '/api/v1/agent/chat', api_data)
            ai_message = response_data.get('data', {}).get('answer', '')
            
            analysis_data = self._extract_json_from_response(ai_message)
            return analysis_data
            
        except Exception as e:
            SimpleLogger.exception(f"测试覆盖度分析失败: {str(e)}")
            return {
                'error': str(e),
                'coverage_percentage': 0,
                'missing_scenarios': [],
                'suggestions': []
            }
    
    def get_conversation_history(self, conversation_id: str, limit: Optional[int] = None) -> List[Dict]:
        """
          获取会话历史记录
          
          参数:
              conversation_id: 要检索的会话ID
              limit: 返回消息数量的限制(可选)
              
          返回:
              会话中的消息列表
        """
        endpoint = f"/conversations/{conversation_id}"
        params = {}
        if limit:
            params['limit'] = limit
            
        return self._request('GET', endpoint, params=params)

    def delete_conversation(self, conversation_id: str) -> Dict:
        """
        删除会话
        
        参数:
            conversation_id: 要删除的会话ID
            
        返回:
            包含删除状态的API响应
        """
        return self._request('DELETE', f"/conversations/{conversation_id}")
    
    def upload_document(
        self,
        file_path: Union[str, Path],
        kb_name: Optional[str] = None,
        chunk_size: Optional[int] = None,
        chunk_overlap: Optional[int] = None,
        metadata: Optional[Dict] = None
    ) -> Dict:
        """
        上传文档到知识库
        
        参数:
            file_path: 要上传的文件路径
            kb_name: 知识库名称(为空则使用默认)
            chunk_size: 文档分割的块大小(可选)
            chunk_overlap: 文档分割的块重叠大小(可选)
            metadata: 附加到文档的元数据(可选)
            
        返回:
            包含上传状态和文档ID的API响应
        """
        kb_name = kb_name or self.default_kb_name
        if not kb_name:
            raise ValueError("未提供知识库名称且未设置默认值")
            
        if isinstance(file_path, str):
            file_path = Path(file_path)
            
        if not file_path.exists():
            raise FileNotFoundError(f"文件未找到: {file_path}")
            
        endpoint = f"/knowledge_bases/{kb_name}/documents"
        
        params = {}
        if chunk_size is not None:
            params['chunk_size'] = chunk_size
        if chunk_overlap is not None:
            params['chunk_overlap'] = chunk_overlap
            
        files = {
            'file': (file_path.name, open(file_path, 'rb')),
        }
        
        if metadata:
            files['metadata'] = (None, json.dumps(metadata), 'application/json')
            
        return self._request('POST', endpoint, params=params, files=files)
    
    def create_knowledge_base(
        self,
        kb_name: str,
        description: Optional[str] = None,
        embedding_model: Optional[str] = None,
        make_default: bool = False
    ) -> Dict:
        """
        创建新知识库
        
        参数:
            kb_name: 要创建的知识库名称
            description: 知识库描述(可选)
            embedding_model: 使用的嵌入模型(可选)
            make_default: 是否设置为客户端的默认知识库
            
        返回:
            包含创建状态的API响应
        """
        data = {'name': kb_name}
        if description:
            data['description'] = description
        if embedding_model:
            data['embedding_model'] = embedding_model
            
        response = self._request('POST', '/knowledge_bases', data=data)
        
        if make_default:
            self.default_kb_name = kb_name
            
        return response
    
    def list_knowledge_bases(self) -> List[Dict]:
        """
        列出所有可用知识库
        
        返回:
            知识库对象列表
        """
        return self._request('GET', '/knowledge_bases')
    
    def delete_knowledge_base(self, kb_name: str) -> Dict:
        """
        删除知识库
        
        参数:
            kb_name: 要删除的知识库名称
            
        返回:
            包含删除状态的API响应
        """
        return self._request('DELETE', f'/knowledge_bases/{kb_name}')
    


# 全局RAGFlow客户端实例
ragflow_client = RAGFlowClient()
